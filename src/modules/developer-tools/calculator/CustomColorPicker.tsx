import { Box, Button } from '@chakra-ui/react';
import { type Color, type ColorChangeHandler, CustomPicker } from 'react-color';
import { Hue, Saturation } from 'react-color/lib/components/common';
import { useTranslation } from 'react-i18next';

const Picker = () => (
  <Box
    background="transparent"
    border="2px solid white"
    borderRadius={['20px', '16px']}
    boxShadow="0px 2px 4px rgba(0, 0, 0, 0.25)"
    boxSizing="border-box"
    height={['20px', '16px']}
    width={['20px', '16px']}
  />
);

const Picker2 = () => (
  <Box
    background="white"
    border="1px solid white"
    borderRadius={['24px', '20px']}
    boxShadow="0px 0px 8px rgba(0, 0, 0, 0.32)"
    height={['24px', '20px']}
    left="-6px"
    position="absolute"
    top="-4px"
    width={['24px', '20px']}
  />
);

type ColorPickerProps = {
  color: Color | undefined;
  onChange: ColorChangeHandler;
  onSubmit: () => void;
  item: string | undefined;
};

const ColorPicker = (props: ColorPickerProps) => {
  const { onChange, onSubmit, item } = props;

  const { t } = useTranslation('dev-tools');

  return (
    <Box
      backgroundColor="white"
      bottom={[0]}
      boxShadow="0px 1px 8px rgba(0, 0, 0, 0.13), 0px 8px 16px rgba(0, 0, 0, 0.07)"
      boxSize="borderBox"
      left={0}
      p={['20px', 0]}
      position={['fixed', 'static']}
      right={0}
      width={['100%', '240px']}
    >
      <Box
        height={['120px', '160px']}
        position="relative"
        width={['100%', '240px']}
      >
        <Saturation {...props} onChange={onChange} pointer={Picker} />
      </Box>
      <Box p={['20px 0 0', '20px']}>
        <Box
          height="12px"
          marginBottom="24px"
          position="relative"
          width={['100%', '200px']}
        >
          <Hue {...props} onChange={onChange} pointer={Picker2} />
        </Box>
        <Button
          data-cy={`calculator.color-picker-submit-${item}`}
          height="40px"
          onClick={() => {
            onSubmit();
          }}
          width={['100%', '200px']}
        >
          {t('calculator.colors-done')}
        </Button>
      </Box>
    </Box>
  );
};

export const ColorPickerWrapped = CustomPicker(ColorPicker);
