import { Box, List, ListItem, Text } from '@chakra-ui/react';
import {
  Button,
  Center,
  Popover,
  PopoverBody,
  PopoverContent,
  PopoverTrigger,
  Portal,
  Spinner,
} from '@chakra-ui/react';
import type React from 'react';
import { type FC, type RefObject, Suspense, useEffect, useState } from 'react';
import type { ColorChangeHandler, ColorResult } from 'react-color';
import { useTranslation } from 'react-i18next';
import { FiCheck } from 'react-icons/fi';
import type { UpdateCalculatorSkinMutationVariables } from 'shared/api';
import { TextInput } from 'shared/components';
import { useShowMessage } from 'shared/hooks/alerts';
import { useIsMobileLayout } from 'shared/hooks/layout';
import {
  useMerchantDetails,
  useUpdateMerchantCalculatorSkin,
} from 'shared/hooks/merchant';
import { calculatorIFrameUrl } from 'shared/lib/env';

import { ColorPickerWrapped } from './CustomColorPicker';

type ColorsInterface = Record<string, string>;
type ColorsProps = {
  innerRef: RefObject<HTMLInputElement>;
  onReloadSet: (bool: boolean) => void;
};

const LoadingIndicator = () => (
  <Center height="10rem" width="100%">
    <Spinner
      boxSize="2rem"
      color="primary.800"
      emptyColor="neutral.100"
      thickness="3px"
    />
  </Center>
);

export const CalculatorColorsList: FC<ColorsProps> = (props) => {
  const { innerRef, onReloadSet } = props;

  const showMessage = useShowMessage();

  const isMobileLayout = useIsMobileLayout();

  const { t } = useTranslation('dev-tools');

  const { data, loading } = useMerchantDetails();

  const [isVisiblePicker, setVisiblePicker] = useState(false);

  const [pickerColor, setPickerColor] = useState<ColorResult>();

  const [activeColorLabel, setActiveColorLabel] = useState<string>();

  const [updateData, setUpdateData] =
    useState<UpdateCalculatorSkinMutationVariables>({
      merchant_id: Number.NaN,
      main: '',
      secondary: '',
      text: '',
      monthly_text: '',
      button: '',
      button_text: '',
      period: '',
      active_period: '',
      period_text: '',
    });

  useEffect(() => {
    setUpdateData({
      merchant_id: data?.merchant?.calculator_skin?.merchant_id || Number.NaN,
      main: data?.merchant?.calculator_skin?.main ?? '',
      secondary: data?.merchant?.calculator_skin?.secondary ?? '',
      text: data?.merchant?.calculator_skin?.text ?? '',
      monthly_text: data?.merchant?.calculator_skin?.monthly_text ?? '',
      button: data?.merchant?.calculator_skin?.button ?? '',
      button_text: data?.merchant?.calculator_skin?.button_text ?? '',
      period: data?.merchant?.calculator_skin?.period ?? '',
      active_period: data?.merchant?.calculator_skin?.active_period ?? '',
      period_text: data?.merchant?.calculator_skin?.period_text ?? '',
    });
  }, [data]);

  const { isLoading, updateCalculatorSkin } = useUpdateMerchantCalculatorSkin();

  const isIFrame = (input: HTMLElement | null): input is HTMLIFrameElement => {
    return input !== null && input.tagName === 'IFRAME';
  };

  const handleChange: ColorChangeHandler = (color: ColorResult) => {
    setPickerColor(color);

    if (activeColorLabel) {
      const iFrame = document.getElementById('calc-iframe');

      if (isIFrame(iFrame) && iFrame.contentWindow && calculatorIFrameUrl) {
        const data = JSON.stringify({
          color: color.hex,
          attr: activeColorLabel,
        });
        iFrame.contentWindow.postMessage(data, calculatorIFrameUrl);
      }

      setUpdateData({ ...updateData, [activeColorLabel]: color.hex });
    }
  };

  const colorToggle = (hex: string, label: string): void => {
    // it need for color-picker initialization. TODO: added functions for converting hex to hsl and hex to rgb
    setPickerColor({
      hex,
      hsl: { h: 0, s: 0, l: 0 },
      rgb: { r: 0, g: 0, b: 0 },
    });
    setVisiblePicker(!isVisiblePicker);
    setActiveColorLabel(label);
  };

  const onSubmit = (): void => {
    setActiveColorLabel(undefined);
    setPickerColor(undefined);
    setVisiblePicker(false);
  };

  const hexOnChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    label: string,
  ): void => {
    if (activeColorLabel === label) {
      setUpdateData({ ...updateData, [activeColorLabel]: e.target?.value });
    }
  };

  const applyColors = async (): Promise<void> => {
    const result = await updateCalculatorSkin({ ...updateData });
    if (result) {
      showMessage(t('calculator.notification-colors-updated'), FiCheck);
      onReloadSet(true);
    }
  };

  const colors: ColorsInterface = Object.keys(updateData)
    .filter((item) => item !== 'merchant_id')
    .reduce((acc, rec) => {
      return {
        ...acc,
        [rec]: updateData[rec as keyof UpdateCalculatorSkinMutationVariables],
      };
    }, {});

  return (
    <Suspense fallback={<LoadingIndicator />}>
      {loading ? (
        <LoadingIndicator />
      ) : (
        <Box
          data-cy="calculator-colors-block"
          marginTop={['13px', 0]}
          maxW={['100%', '242px']}
          position="relative"
          w="100%"
        >
          <Text mb={[3, 3]} textStyle="h4">
            {t('calculator.colors-title')}
          </Text>
          <List
            border="1px solid"
            borderColor="neutral.100"
            borderRadius="4px"
            mb={['20px', 0]}
          >
            {Object.keys(colors).map((item) => (
              <ListItem
                _last={{ borderBottom: 'none' }}
                alignItems="center"
                borderBottom="1px solid"
                borderColor="neutral.100"
                display="flex"
                h="41px"
                justifyContent="space-between"
                key={item}
                px={[0, 3]}
                py={[0, 2.5]}
              >
                <Text
                  color="neutral.900"
                  fontSize="14px"
                  lineHeight="20px"
                  ml={['12px', 0]}
                  textStyle="calculator1"
                >
                  {t(`calculator.color-${item}`)}
                </Text>
                <Box display="flex">
                  {isMobileLayout ? (
                    <Box
                      borderColor="neutral.100"
                      borderLeft="1px solid"
                      borderRight="1px solid"
                      h="41px"
                      px="16px"
                      py="10px"
                      w="52px"
                    >
                      <Box
                        alignSelf="start"
                        bg={
                          isVisiblePicker && activeColorLabel === item
                            ? pickerColor?.hex
                            : colors[item]
                        }
                        border="1px solid"
                        borderColor="neutral.100"
                        borderRadius="2px"
                        h="20px"
                        onClick={() => {
                          colorToggle(colors[item], item);
                        }}
                        w="20px"
                      />
                    </Box>
                  ) : (
                    <Popover placement="left-start" trigger="click">
                      {({ onClose }) => (
                        <Box
                          borderColor="neutral.100"
                          height={['41px']}
                          px="12px"
                          py="10px"
                          width="44px"
                        >
                          <PopoverTrigger>
                            <Box
                              alignSelf="start"
                              bg={
                                isVisiblePicker && activeColorLabel === item
                                  ? pickerColor?.hex
                                  : colors[item]
                              }
                              border="1px solid"
                              borderColor="neutral.100"
                              borderRadius="2px"
                              data-cy={`calculator.color-${item}`}
                              h="20px"
                              onClick={() => {
                                colorToggle(colors[item], item);
                              }}
                              w="20px"
                            />
                          </PopoverTrigger>
                          <PopoverContent
                            data-cy={`calculator.color-picker-${item}`}
                            marginRight={[0, '125px']}
                            marginTop={[0, '-10px']}
                            mb={2}
                            p={0}
                          >
                            <PopoverBody
                              fontSize="12px"
                              lineHeight="16px"
                              padding={0}
                            >
                              {!!pickerColor && (
                                <ColorPickerWrapped
                                  color={pickerColor?.hex}
                                  item={activeColorLabel}
                                  onChange={handleChange}
                                  onSubmit={() => {
                                    onSubmit();
                                    onClose();
                                  }}
                                />
                              )}
                            </PopoverBody>
                          </PopoverContent>
                        </Box>
                      )}
                    </Popover>
                  )}
                  <Box
                    onClick={() => {
                      setActiveColorLabel(item);
                    }}
                  >
                    <TextInput
                      additionalStyles={{
                        width: '64px',
                        height: '20px',
                        fontSize: '14px',
                        lineHeight: '18px',
                        _focus: { pl: 0, pr: 0 },
                        border: '1px solid',
                        borderColor: 'neutral.100',
                        paddingLeft: '4px',
                      }}
                      color="neutral.900"
                      data-cy={`calculator.color-picker-input-${item}`}
                      isDisabled={isVisiblePicker}
                      ml={['16px', 0]}
                      mr={['16px', 0]}
                      name={`calculator.color-${item}`}
                      onChange={(e) => {
                        hexOnChange(e, item);
                      }}
                      py={['10px']}
                      textStyle="calculator1"
                      value={colors[item]}
                      variant="unstyled"
                    />
                  </Box>
                </Box>
              </ListItem>
            ))}
            {!isMobileLayout && (
              <ListItem
                alignItems="center"
                display="flex"
                key="apply-button"
                px={0}
                py={0}
              >
                <Button
                  _focus={{ backgroundColor: 'primary.800' }}
                  _hover={{ backgroundColor: 'primary.700' }}
                  _selected={{ backgroundColor: 'primary.800' }}
                  bg="primary.800"
                  borderRadius="0 0 4px 4px"
                  color="white"
                  data-cy="calculator.color-picker-apply"
                  disabled={isLoading}
                  h="38px"
                  onClick={async () => {
                    await applyColors();
                  }}
                  w="100%"
                >
                  {t('calculator.colors-apply')}
                </Button>
              </ListItem>
            )}
          </List>
          {!!isMobileLayout && (
            <>
              {!!isVisiblePicker && !!pickerColor && (
                <Portal containerRef={innerRef}>
                  <Box width="100%">
                    <ColorPickerWrapped
                      color={pickerColor?.hex}
                      item={activeColorLabel}
                      onChange={handleChange}
                      onSubmit={() => {
                        onSubmit();
                      }}
                    />
                  </Box>
                </Portal>
              )}
              <Button
                _focus={{ backgroundColor: 'primary.800' }}
                _hover={{ backgroundColor: 'primary.700' }}
                _selected={{ backgroundColor: 'primary.800' }}
                bg="primary.800"
                borderRadius="4px"
                color="white"
                disabled={isLoading}
                h="48px"
                mt="20px"
                onClick={async () => {
                  await applyColors();
                }}
                w="100%"
              >
                {t('calculator.colors-apply')}
              </Button>
            </>
          )}
        </Box>
      )}
    </Suspense>
  );
};
