import { Box, Collapse, Icon, Text } from '@chakra-ui/react';
import type { TerminalFormSchemaType } from 'modules/terminal/Terminal.schema';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FiInfo } from 'react-icons/fi';
import { CypressTerminalKeys } from 'shared/constants/cypress-keys/terminal-page';

export const PracticeModeDisclaimer = () => {
  const { watch } = useFormContext<TerminalFormSchemaType>();
  const isPracticeMode = watch('isPracticeMode');
  const { t } = useTranslation('terminal');

  return (
    <Collapse
      animateOpacity
      in={isPracticeMode}
      onAnimationComplete={() => window.dispatchEvent(new Event('resize'))}
      unmountOnExit
    >
      <Box
        alignItems="flex-start"
        data-cy={CypressTerminalKeys.PRACTICE_MODE_DISCLAIMER}
        display="flex"
        justifyContent="center"
        mb={[0, 8]}
        mt={[5, 0]}
      >
        <Icon
          as={FiInfo}
          boxSize={6}
          color="primary.700"
          display={['none', 'inline-block']}
          mr={3}
          mt={2}
        />
        <Text
          bg="primary.100"
          borderRadius="4px"
          color="primary.900"
          maxW={['100%', '400px']}
          px={3}
          py={2}
          textStyle="body2"
        >
          {t('practice-mode.disclaimer')}
        </Text>
      </Box>
    </Collapse>
  );
};
