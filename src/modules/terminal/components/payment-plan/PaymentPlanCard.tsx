import { Box, GridItem, SimpleGrid, Text } from '@chakra-ui/react';
import { PaymentPlanLogo } from 'modules/terminal/components/payment-plan/PaymentPlanLogo';
import type { ReactNode } from 'react';
import { ApplicationScheduleType } from 'shared/api';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { Selectable } from 'shared/components';
import { useIsCashier } from 'shared/hooks/user';

import type { TerminalPaymentPlan } from '../../Terminal.types';
import { CashierBonusLabel } from '../cashier-bonus-label';

type PaymentPlanCardProps = {
  isDisabled?: boolean;
  isSelected: boolean;
  isPracticeMode?: boolean;
  plan: TerminalPaymentPlan;
  onClick: () => void;
  rightContent?: ReactNode;
  bottomContent?: ReactNode;
  dataCy?: string;
  applicationAmount?: number;
};

export const PaymentPlanCard = ({
  isSelected,
  isDisabled,
  isPracticeMode,
  plan: { logoUrl, name, periodLimitsHint, cashierBonusPct, scheduleType },
  onClick,
  rightContent,
  bottomContent,
  dataCy,
  applicationAmount,
}: PaymentPlanCardProps) => {
  const isCashier = useIsCashier();
  const shouldShowBonus =
    isCashier && scheduleType === ApplicationScheduleType.REGULAR;

  return (
    <Selectable
      _disabled={{
        color: 'neutral.800',
        bg: 'rgba(243, 245, 247, 0.5)',
        borderColor: 'rgba(243, 245, 247, 0.5)',
        cursor: 'not-allowed',
      }}
      alignItems="flex-start"
      color="neutral.900"
      colorScheme={isPracticeMode ? ColorSchemes.NEUTRAL : ColorSchemes.PRIMARY}
      data-cy={dataCy}
      display="flex"
      isDisabled={isDisabled}
      isSelected={isSelected}
      justifyContent="flex-start"
      onClick={onClick}
      position="relative"
    >
      <SimpleGrid
        alignItems="center"
        columns={3}
        justifyItems="center"
        spacing={3}
        templateAreas={`"icon description${shouldShowBonus ? ' bonus' : ''}"${
          bottomContent
            ? ` ".  bottomContent${shouldShowBonus ? ' bottomContent' : ''}"`
            : ''
        }`}
        templateColumns="36px auto"
        templateRows={`40px${bottomContent ? ' auto' : ''}`}
        w="100%"
      >
        <GridItem gridArea="icon">
          <PaymentPlanLogo
            isPracticeMode={isPracticeMode}
            isSelected={isSelected}
            logoUrl={logoUrl}
            name={name}
          />
        </GridItem>
        <GridItem
          alignItems="center"
          display="flex"
          gridArea="description"
          justifyContent="space-between"
          justifySelf="stretch"
        >
          <Box flexGrow={1}>
            <Text textStyle="body1-highlight">{name}</Text>
            <Text textStyle="caption">{periodLimitsHint}</Text>
          </Box>
          {!shouldShowBonus && rightContent}
        </GridItem>
        {shouldShowBonus && (
          <GridItem
            gridArea="bonus"
            display="flex"
            alignItems="center"
            justifySelf="flex-end"
          >
            <CashierBonusLabel
              applicationAmount={applicationAmount}
              cashierBonusPct={cashierBonusPct}
            />
            {rightContent}
          </GridItem>
        )}
        {!!bottomContent && (
          <GridItem
            borderColor="primary.300"
            borderTop="1px solid"
            display="flex"
            gridArea="bottomContent"
            justifySelf="stretch"
            pt={4}
          >
            {bottomContent}
          </GridItem>
        )}
      </SimpleGrid>
    </Selectable>
  );
};
