import { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  LocizeNamespaces,
  LocizeTerminalKeys,
} from 'shared/constants/localization-keys';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { GlobalStateContext } from 'shared/hooks/state';
import type { GraphQLValidationError } from 'shared/types';

import { useUserEligibilityLazyQuery } from './queries.gen';
import {
  type CreditEligibilityCheckResponse,
  type CreditEligibilityFormData,
  CreditEligibilityStatus,
} from './types';

const ELIGIBILITY_STATUS_MAP: Record<string, CreditEligibilityStatus> = {
  '1': CreditEligibilityStatus.ELIGIBLE,
  '0': CreditEligibilityStatus.MORE_INFO_NEEDED,
  '-1': CreditEligibilityStatus.NOT_ELIGIBLE,
};

export const useCreditEligibilityCheck = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<
    CreditEligibilityCheckResponse | undefined
  >();
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string[]> | undefined
  >();
  const handleGenericError = useHandleGenericError();
  const { merchantId } = useContext(GlobalStateContext);
  const [getUserEligibility] = useUserEligibilityLazyQuery();
  const { t } = useTranslation(LocizeNamespaces.TERMINAL);

  const checkEligibility = async (data: CreditEligibilityFormData) => {
    setIsLoading(true);
    setValidationErrors(undefined);
    try {
      if (!merchantId) {
        throw new Error('Merchant ID is required');
      }

      const response = await getUserEligibility({
        fetchPolicy: 'network-only',
        variables: {
          merchant_id: merchantId,
          pin: data.idCode,
          email: data.email,
          conditions_agreement: data.privacyPolicyConsent,
          newsletter_agreement: data.newsletterConsent ?? false,
          is_user_identified_by_cashier: data.customerIdentificationConsent,
        },
      });

      if (response.error?.graphQLErrors) {
        const validationError = response.error.graphQLErrors.find(
          (err): err is GraphQLValidationError =>
            err.message === 'validation' && 'validation' in err,
        );

        if (validationError?.validation) {
          setValidationErrors(validationError.validation);
          return;
        }
      }

      const eligibilityResult = response.data?.user_eligibility;

      const status =
        ELIGIBILITY_STATUS_MAP[eligibilityResult ?? ''] ??
        CreditEligibilityStatus.NOT_ELIGIBLE;

      const parsedResponse: CreditEligibilityCheckResponse = {
        status,
        customerName: `${t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_CUSTOMER)} (${data.idCode})`,
      };

      setResult(parsedResponse);
      return parsedResponse;
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'graphQLErrors' in error) {
        const graphQLError = error as {
          graphQLErrors: Array<{
            message: string;
            validation?: Record<string, string[]>;
          }>;
        };
        const validationError = graphQLError.graphQLErrors.find(
          (err) => err.message === 'validation' && err.validation,
        );

        if (validationError) {
          setValidationErrors(validationError.validation);
          return;
        }
      }

      handleGenericError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const resetResult = () => {
    setResult(undefined);
    setValidationErrors(undefined);
  };

  return {
    isLoading,
    result,
    validationErrors,
    checkEligibility,
    resetResult,
  };
};
