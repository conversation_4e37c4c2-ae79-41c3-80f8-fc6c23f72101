import { z } from 'zod';

export enum CreditEligibilityStatus {
  ELIGIBLE = 'ELIGIBLE',
  NOT_ELIGIBLE = 'NOT_ELIGIBLE',
  MORE_INFO_NEEDED = 'MORE_INFO_NEEDED',
}

export const CreditEligibilityFormSchema = z.object({
  amount: z.number().min(1, 'invalid-format').max(5000, 'amount-not-allowed'),
  idCode: z.string().min(1, 'required'),
  email: z.string().email('invalid-format'),
  privacyPolicyConsent: z.boolean().refine((val) => val === true, {
    message: 'required',
  }),
  newsletterConsent: z.boolean(),
  customerIdentificationConsent: z.boolean().refine((val) => val === true, {
    message: 'required',
  }),
});

export type CreditEligibilityFormData = z.infer<
  typeof CreditEligibilityFormSchema
>;

export interface CreditEligibilityCheckRequest {
  amount: number;
  idCode: string;
  email: string;
  privacyPolicyConsent: boolean;
  newsletterConsent: boolean;
  customerIdentificationConsent: boolean;
}

export interface CreditEligibilityCheckResponse {
  status: CreditEligibilityStatus;
  customerName?: string;
}

export interface CreditEligibilityModalProps {
  isOpen: boolean;
  onClose: () => void;
}
