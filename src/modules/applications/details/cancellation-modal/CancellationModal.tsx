import {
  Box,
  Button,
  Icon,
  Modal,
  ModalBody,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>eader,
  ModalOverlay,
  Text,
} from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { CANCELLATION_REASONS } from 'modules/applications/details/cancellation-modal/CancellationModal.contants';
import { useEffect } from 'react';
import { type Control, type FieldValues, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { FiAlertCircle } from 'react-icons/fi';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import {
  ButtonWithLoader,
  ControlledSelect,
  ModalCloseButton,
  TextInput,
} from 'shared/components';
import {
  LocizeApplicationsKeys,
  LocizeNamespaces,
} from 'shared/constants/localization-keys';

import {
  useApplicationCancel,
  useCancellationReasonOptions,
} from './CancellationModal.hooks';
import {
  CancellationSchema,
  type CancellationSchemaType,
} from './CancellationModal.schema';

type Props = {
  isOpen: boolean;
  applicationId: number;
  onClose: () => void;
};

export const CancellationModal = ({
  isOpen,
  applicationId,
  onClose,
}: Props) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const {
    register,
    control,
    watch,
    formState: { errors, isValid },
    setError,
    handleSubmit,
    resetField,
  } = useForm<CancellationSchemaType>({
    defaultValues: { reason: CANCELLATION_REASONS[0] },
    resolver: zodResolver(CancellationSchema),
    mode: 'onChange',
  });
  const [reason, otherReason] = watch(['reason', 'otherReason']);

  const reasonSelectItems = useCancellationReasonOptions();

  const { onSubmit, isSubmitting } = useApplicationCancel({
    onSuccess: onClose,
    setError,
    applicationId,
  });

  useEffect(() => {
    if (!otherReason) {
      return;
    }

    resetField('otherReason');
  }, [reason]);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      scrollBehavior="inside"
      trapFocus={false}
    >
      <ModalOverlay display={['none', 'flex']} />
      <ModalContent
        as="form"
        data-cy="application-cancellation-modal-content"
        maxW="29rem"
        onSubmit={handleSubmit(onSubmit)}
      >
        <ModalHeader>{t('cancellation-modal.title')}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <Text mb={4} textStyle="body2">
            {t('cancellation-modal.description')}
          </Text>
          <ControlledSelect
            control={control as unknown as Control<FieldValues>}
            data-cy="application-cancellation-reason"
            items={reasonSelectItems}
            label={t('cancellation-modal.labels.reason')}
            name="reason"
          />
          {reason ===
            LocizeApplicationsKeys.CANCELLATION_MODAL_REASONS_OTHER && (
            <TextInput
              data-cy="application-cancellation-other-reason"
              label={t('cancellation-modal.labels.other-reason')}
              {...register('otherReason')}
              error={
                errors.otherReason?.message
                  ? t(`common:forms.${errors.otherReason.message}`)
                  : undefined
              }
            />
          )}
          <Box
            alignItems="center"
            bg="red.100"
            borderRadius="4px"
            data-cy="application-cancellation-modal-disclaimer"
            display="flex"
            mt={[3, 5]}
            px={3}
            py={2}
          >
            <Icon as={FiAlertCircle} boxSize={5} color="red.700" mr={2} />
            <Text color="red.900" textStyle="body2">
              {t('cancellation-modal.disclaimer')}
            </Text>
          </Box>
        </ModalBody>

        <ModalFooter>
          <Button
            colorScheme={ColorSchemes.SECONDARY}
            display={['none', 'inline-flex']}
            isDisabled={isSubmitting}
            mr={[0, 3]}
            onClick={onClose}
          >
            {t('common:forms.cancel')}
          </Button>
          <ButtonWithLoader
            data-cy="application-cancellation-submit"
            isDisabled={isSubmitting || !isValid}
            isLoading={isSubmitting}
            type="submit"
          >
            {t(LocizeApplicationsKeys.APPLICATION_UPDATE_FORM_SUBMIT)}
          </ButtonWithLoader>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};
