import { Box, Button, useDisclosure } from '@chakra-ui/react';
import type { ReactNode } from 'react';
import { type FC, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { FiXCircle } from 'react-icons/fi';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { LocizeNamespaces } from 'shared/constants/localization-keys';
import { useShowMessage } from 'shared/hooks/alerts';
import type { ApplicationType } from 'shared/types';

import { getPendingRefundRequest } from './../shared';
import { RefundingModal } from './RefundingModal';

type ActionButtonProps = {
  icon: ReactNode;
  text: string;
  onClick: () => void;
  dataCy: string;
  colorScheme?: ColorSchemes;
};

const ActionButton: FC<ActionButtonProps> = ({
  text,
  icon,
  onClick,
  dataCy,
  colorScheme = ColorSchemes.PRIMARY,
}) => {
  return (
    <Button
      colorScheme={colorScheme}
      data-cy={dataCy}
      justifyContent="flex-start"
      // @ts-expect-error note
      leftIcon={icon}
      onClick={onClick}
      size="sm"
      variant="ghost"
      width="full"
    >
      {text}
    </Button>
  );
};

type Props = Pick<
  ApplicationType,
  'refunded_amount' | 'direct_payment_refunds' | 'id' | 'requested_amount'
>;

export const ApplicationRefund = ({
  refunded_amount,
  direct_payment_refunds,
  id,
  requested_amount,
}: Props) => {
  const { t } = useTranslation(LocizeNamespaces.APPLICATIONS);
  const { onClose, onOpen, isOpen } = useDisclosure();
  const showMessage = useShowMessage();

  const onRefundSuccess = useCallback(async () => {
    onClose();
    showMessage(t('notifications.refund-requested'), FiXCircle);
  }, [t, showMessage, onClose]);

  if (getPendingRefundRequest(direct_payment_refunds || null)) {
    return null;
  }

  return (
    <Box
      border="1px solid"
      borderColor="neutral.150"
      borderRadius="4px"
      data-cy="application-refund"
      p={4}
    >
      <ActionButton
        colorScheme={ColorSchemes.RED}
        dataCy="application-refund-toggle"
        icon={<FiXCircle />}
        onClick={onOpen}
        text={t('details.refund')}
      />

      <RefundingModal
        allowedRefundAmount={refunded_amount}
        applicationId={id}
        initialAmount={requested_amount}
        isOpen={isOpen}
        onClose={onClose}
        onSuccess={onRefundSuccess}
      />
    </Box>
  );
};
