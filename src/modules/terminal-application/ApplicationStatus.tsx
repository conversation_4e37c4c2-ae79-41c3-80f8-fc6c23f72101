import { Box, Icon, Text, useTheme } from '@chakra-ui/react';
import lottie from 'lottie-web';
import clockAnimationData from 'modules/terminal-application/clock.json';
import practiceClockAnimationData from 'modules/terminal-application/clock-practice.json';
import { useEffect, useMemo, useRef } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { CustomCheckCircle, CustomCrossCircle } from 'shared/components';
import { useFormattedAmount } from 'shared/hooks/utils';
import { ApplicationLinkAction } from 'shared/types';

export enum ApplicationState {
  PENDING = 0,
  SIGNED = 1,
  REJECTED = 2,
}

export type ApplicationStatusProps = {
  state: ApplicationState;
  action: ApplicationLinkAction;
  isPracticeMode: boolean;
  isShowSubtitle: boolean;
  withCashierBonus?: boolean;
  cashierBonusAmount?: number;
};

function useStateIcon(state: ApplicationState, isPracticeMode: boolean) {
  const chakraTheme = useTheme();
  const animContainer = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!animContainer.current) {
      return;
    }

    const animation = lottie.loadAnimation({
      container: animContainer.current,
      renderer: 'svg',
      loop: true,
      autoplay: true,
      animationData: isPracticeMode
        ? practiceClockAnimationData
        : clockAnimationData,
    });

    return () => {
      animation.destroy();
    };
  }, [isPracticeMode]);

  return useMemo(() => {
    switch (state) {
      case ApplicationState.PENDING:
        return (
          <Box boxSize={10} data-cy="status-icon-pending" ref={animContainer} />
        );
      case ApplicationState.SIGNED:
        return (
          <Icon
            as={CustomCheckCircle}
            boxSize={10}
            color={isPracticeMode ? 'neutral.800' : 'green.700'}
            data-cy="status-icon-signed"
            sx={{
              '--fill-color': isPracticeMode
                ? chakraTheme.colors.neutral['100']
                : chakraTheme.colors.green['100'],
            }}
          />
        );
      case ApplicationState.REJECTED:
        return (
          <Icon
            as={CustomCrossCircle}
            boxSize={10}
            color={isPracticeMode ? 'neutral.800' : 'red.700'}
            data-cy="status-icon-rejected"
            sx={{
              '--fill-color': isPracticeMode
                ? chakraTheme.colors.neutral['100']
                : chakraTheme.colors.red['100'],
            }}
          />
        );
    }
  }, [state, isPracticeMode, chakraTheme]);
}

function useTextData(
  state: ApplicationState,
  action: ApplicationLinkAction,
): { title: string; subtitle?: string } {
  const { t } = useTranslation('terminal');

  return useMemo(() => {
    switch (state) {
      case ApplicationState.PENDING: {
        switch (action) {
          case ApplicationLinkAction.SendLink:
            return {
              title: t('application-status.title-send-link'),
              subtitle: t('application-status.subtitle-send-link'),
            };
          case ApplicationLinkAction.GenerateQR:
            return {
              title: t('application-status.title-qr-code'),
              subtitle: t('application-status.subtitle-qr-code'),
            };
          case ApplicationLinkAction.CopyLink:
            return {
              title: t('application-status.title-copy-link'),
              subtitle: t('application-status.subtitle-copy-link'),
            };
          case ApplicationLinkAction.OpenInNewTab:
            return {
              title: t('application-status.title-new-tab'),
              subtitle: t('application-status.subtitle-new-tab'),
            };
        }
        break;
      }
      case ApplicationState.SIGNED:
        return { title: t('application-status.title-signed') };
      case ApplicationState.REJECTED:
        return { title: t('application-status.title-rejected') };
    }
  }, [state, action, t]);
}

export const ApplicationStatus = ({
  state,
  action,
  isPracticeMode,
  isShowSubtitle,
  withCashierBonus,
  cashierBonusAmount,
}: ApplicationStatusProps) => {
  const { t } = useTranslation('terminal');
  const stateIcon = useStateIcon(state, isPracticeMode);
  const { title, subtitle } = useTextData(state, action);

  const amount = useFormattedAmount(cashierBonusAmount ?? 0, { currency: '' });

  return (
    <Box alignItems="center" display="flex" flexDirection="column">
      <Box mb={5}>{stateIcon}</Box>
      <Text data-cy="status-title" mb={2} textAlign="center" textStyle="h3">
        {title}
      </Text>
      {withCashierBonus && (
        <Text
          textStyle="body1"
          fontWeight="semibold"
          mt={3}
          display="flex"
          alignItems="center"
          gap={1}
        >
          <Trans
            t={t}
            i18nKey="cashier-bonus.earned-disclaimer"
            components={{
              textComponent: (
                <Text
                  textStyle="h4"
                  as="span"
                  color="green.600"
                  marginTop="-2px"
                />
              ),
            }}
            values={{
              amount,
            }}
          />
        </Text>
      )}
      {!!isShowSubtitle && !!subtitle && (
        <Text data-cy="status-subtitle" textAlign="center" textStyle="body1">
          {subtitle}
        </Text>
      )}
    </Box>
  );
};
