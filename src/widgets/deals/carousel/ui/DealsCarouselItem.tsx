import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { CarouselItem } from '@components/ui/carousel';
import { useIsMobileView } from '@hooks/system';
import { cn } from '@utils/tailwind';
import { useEffect, useRef, useState } from 'react';
import { useToggle } from 'react-use';

import { NOISE_TEXTURE_STYLES } from '@/shared/config/styles';

interface DealsCarouselItemProps {
  imgSrc: string;
  title: string;
  ctaLabel: string;
  onCtaClick: () => void;
  isOnlySlide?: boolean;
  shouldLoadOnRedirect?: boolean;
}

export const DealsCarouselItem = ({
  imgSrc,
  title,
  ctaLabel,
  onCtaClick,
  isOnlySlide = false,
  shouldLoadOnRedirect = false,
}: DealsCarouselItemProps) => {
  const isMobileView = useIsMobileView();
  const [isRedirecting, setIsRedirecting] = useToggle(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const itemRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        threshold: 0.05,
        rootMargin: '100px',
      },
    );

    if (itemRef.current) {
      observer.observe(itemRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const onButtonClick = () => {
    if (shouldLoadOnRedirect) {
      setIsRedirecting();
      setTimeout(() => {
        setIsRedirecting(false);
      }, 2000);
    }
    onCtaClick();
  };

  return (
    <CarouselItem
      key={title}
      className={cn(
        'mr-1.5 basis-[85%] pl-4',
        isOnlySlide && 'mr-0 basis-full pl-4',
      )}
    >
      <div
        ref={itemRef}
        className="relative flex h-[20rem] w-full flex-col justify-end rounded-2xl overflow-hidden px-6 py-8"
      >
        <div className="absolute inset-0 bg-gray-300 rounded-2xl" />

        {isVisible && (
          <img
            src={imgSrc}
            alt={title}
            onLoad={handleImageLoad}
            className={cn(
              'absolute inset-0 h-full w-full object-cover rounded-2xl transition-opacity duration-300',
              imageLoaded ? 'opacity-100' : 'opacity-0',
            )}
          />
        )}
        <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-black/40 rounded-2xl" />

        <div
          className="absolute inset-0 rounded-2xl"
          style={NOISE_TEXTURE_STYLES}
        />

        <Typography
          variant={isMobileView ? 'xs' : 'm'}
          className="max-w-[31.75rem] text-white relative z-10"
        >
          {title}
        </Typography>
        <Button
          loading={isRedirecting}
          onClick={onButtonClick}
          size="small"
          variant="white"
          className="relative z-10 mt-6 w-fit"
        >
          {ctaLabel}
        </Button>
      </div>
    </CarouselItem>
  );
};
