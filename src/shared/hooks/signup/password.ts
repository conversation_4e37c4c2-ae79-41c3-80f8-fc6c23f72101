import type { ApolloError } from '@apollo/client';
import type { LoginHookParams } from 'modules/auth/login/types';
import { useState } from 'react';
import { useStoreUserFromInviteByPasswordMutation } from 'shared/api';
import { useHandleGenericError } from 'shared/hooks/alerts';
import { extractGraphqlErrors } from 'shared/utils/graphql';

// Define type for GraphQL error with validation
type GraphQLErrorWithValidation = {
  validation?: Record<string, string>;
};

export function usePasswordSignUp({ onSuccess }: LoginHookParams) {
  const [error, setError] = useState<ApolloError | Error | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<
    string,
    string
  > | null>(null);
  const handleGenericError = useHandleGenericError();
  const [signUpWithPassword, { loading }] =
    useStoreUserFromInviteByPasswordMutation({
      onCompleted: (data) => {
        if (data.success) {
          onSuccess();
        } else {
          setError(new Error('Wrong data'));
        }
      },
      onError: (err) => {
        handleGenericError(err);
        const [graphqlerrors] = extractGraphqlErrors(err);
        const errorWithValidation =
          graphqlerrors[0] as GraphQLErrorWithValidation;
        if (errorWithValidation.validation) {
          setValidationErrors(errorWithValidation.validation);
        }
        setError(err);
      },
    });

  const resetValidationErrors = () => {
    setValidationErrors(null);
  };

  return {
    isLoading: loading,
    error,
    validationErrors,
    resetValidationErrors,
    signUpWithPassword,
  };
}
