import type { PropsWithChildren } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { LoginRoute } from 'shared/constants/routes';
import { useRoleAndRegionCheck } from 'shared/hooks/application';
import { useCurrentUser } from 'shared/hooks/user';
import { type MerchantPermissions, UserPermissions } from 'shared/types';
import { type AppRegions, hasPermissions } from 'shared/utils';

import { Loader } from '../index';
import { DefaultRedirect } from './DefaultRedirect';

type PermissionGuardProps = {
  roles?: Array<MerchantPermissions>;
  notImplementedRedirect?: string;
  regions?: Array<AppRegions>;
};

export const PermissionGuard = ({
  children,
  roles,
  notImplementedRedirect,
  regions,
}: PropsWithChildren<PermissionGuardProps>) => {
  const { loading, user } = useCurrentUser();
  const location = useLocation();
  const isSuperAdmin =
    !!user && hasPermissions(user.permission_bits, [UserPermissions.Admin]);

  const isValidRoleAndRegion = useRoleAndRegionCheck()({ roles, regions });

  if (loading) {
    return <Loader fullScreen />;
  }

  if (!user) {
    return (
      <Navigate
        replace={true}
        to={LoginRoute.format({
          redirectUrl: location.pathname + location.search,
        })}
      />
    );
  }

  const isAllowed = isSuperAdmin || isValidRoleAndRegion;

  if (!isAllowed) {
    return <DefaultRedirect />;
  }

  if (notImplementedRedirect) {
    window.location.replace(notImplementedRedirect);
    return null;
  }

  return <>{children}</>;
};
