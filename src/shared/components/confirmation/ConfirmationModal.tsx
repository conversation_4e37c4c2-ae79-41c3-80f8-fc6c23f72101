import {
  Modal,
  ModalContent,
  ModalOverlay,
  useDisclosure,
} from '@chakra-ui/react';
import { cloneElement } from 'react';

import type { ConfirmationDisplayElementProps } from './common';
import { ConfirmationContent } from './ConfirmationContent';

export const ConfirmationModal = ({
  title,
  actionText,
  onAction,
  trigger,
  cyPrefix,
}: ConfirmationDisplayElementProps) => {
  const { isOpen, onClose, onOpen } = useDisclosure();

  return (
    <>
      {/* @ts-expect-error note */}
      {cloneElement(trigger, { ...trigger.props, onClick: onOpen })}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent
          borderRadius="4px"
          data-cy={[cyPrefix, 'confirmation-modal-content'].join('-')}
          h="auto"
          minH="7rem"
          minW="15rem"
          p={5}
          pt={4}
          w="auto"
        >
          <ConfirmationContent
            actionText={actionText}
            cyPrefix={cyPrefix}
            onAction={onAction}
            onClose={onClose}
            title={title}
          />
        </ModalContent>
      </Modal>
    </>
  );
};
