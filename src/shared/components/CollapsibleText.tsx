import {
  Box,
  type BoxProps,
  Link,
  Text,
  type TextProps,
  useDisclosure,
} from '@chakra-ui/react';
import type { ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

type CollapsibleTextProps = {
  textProps?: TextProps;
  children?: ReactNode;
  dataCy?: string;
};

export const CollapsibleText = ({
  textProps,
  children,
  dataCy,
  ...boxProps
}: CollapsibleTextProps & BoxProps) => {
  const { t } = useTranslation('common');
  const { isOpen, onToggle } = useDisclosure({ defaultIsOpen: false });

  return (
    <Box
      bg="neutral.50"
      borderRadius="4px"
      data-cy={dataCy}
      px={3}
      py={2}
      {...boxProps}
    >
      <Text
        color="neutral.900"
        maxH={isOpen ? 'auto' : '1.25rem'}
        overflow="hidden"
        textOverflow="ellipsis"
        textStyle="body2"
        whiteSpace={isOpen ? 'pre-wrap' : 'nowrap'}
        {...textProps}
      >
        {children}
      </Text>
      {!!isOpen && <br />}
      <Link
        color="neutral.900"
        onClick={onToggle}
        textDecor="underline"
        textStyle="body2"
      >
        {isOpen ? t('text-block.hide') : t('text-block.show')}
      </Link>
    </Box>
  );
};
