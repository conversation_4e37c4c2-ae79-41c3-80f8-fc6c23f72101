export const LocizeApplicationsKeys = {
  UNDO_REQUEST_MODAL_TITLE: 'undo-request-modal-title',
  TOOLTIP_UNDO_MODIFICATION: 'tooltip.undo-modification',
  TOOLTIP_UNDO_CANCELLATION: 'tooltip.undo-cancellation',
  AMOUNT_MODIFICATION_MODAL_REASONS_NOT_AVAILABLE:
    'amount-modification-modal.reasons.not-available',
  AMOUNT_MODIFICATION_MODAL_REASONS_ORDER_PARTIALLY_RETURNED:
    'amount-modification-modal.reasons.order-partially-returned',
  AMOUNT_MODIFICATION_MODAL_REASONS_ITEMS_REPLACED:
    'amount-modification-modal.reasons.items-replaced',
  AMOUNT_MODIFICATION_MODAL_REASONS_DISCOUNT_APPLIED:
    'amount-modification-modal.reasons.discount-applied',
  AMOUNT_MODIFICATION_MODAL_REASONS_INCORRECT_INITIAL_PRICE:
    'amount-modification-modal.reasons.incorrect-initial-price',
  AMOUNT_MODIFICATION_MODAL_REASONS_OTHER:
    'amount-modification-modal.reasons.other',
  CANCELLATION_MODAL_REASONS_CUSTOMER_RETURNED:
    'cancellation-modal.reasons.customer-returned',
  CANCELLATION_MODAL_REASONS_DELIVERY: 'cancellation-modal.reasons.delivery',
  CANCELLATION_MODAL_REASONS_NEW_CONTRACT:
    'cancellation-modal.reasons.new-contract',
  CANCELLATION_MODAL_REASONS_DUPLICATE: 'cancellation-modal.reasons.duplicate',
  CANCELLATION_MODAL_REASONS_OTHER: 'cancellation-modal.reasons.other',
  APPLICATION_UPDATE_FORM_SUBMIT: 'application-update-form.submit',
  APPLICATION_UPDATE_UNDO: 'application-update-undo',
  NOTIFICATIONS_SUCCESSFUL_REQUEST: 'notifications.successful-request',
  MODIFICATION_HISTORY_TABLE_COLUMN_REQUEST:
    'modification-history-table.column.request',

  MODIFICATION_HISTORY_TABLE_COLUMN_DATE:
    'modification-history-table.column.date',
  MODIFICATION_HISTORY_TABLE_COLUMN_AMOUNT:
    'modification-history-table.column.amount',
  MODIFICATION_HISTORY_TABLE_COLUMN_REASON:
    'modification-history-table.column.reason',
  MODIFICATION_HISTORY_TABLE_COLUMN_REQUEST_BY:
    'modification-history-table.column.request-by',
  MODIFICATION_HISTORY_REQUEST_CANCELLATION:
    'modification-history.request.cancellation',
  MODIFICATION_HISTORY_REQUEST_AMOUNT_MODIFICATION:
    'modification-history.request.amount-modification',

  // APPLICATION TABLE
  ApplicationsTable: {
    APPLICATIONS_TABLE_STATUS_FILTER_NO_ACTION_PENDING_OPTION:
      'statuses.no-action-pending',
    APPLICATIONS_TABLE_STATUS_FILTER_NO_USER_OPTION: 'statuses.no-user',
    APPLICATIONS_TABLE_STATUS_FILTER_COMPLETED_OPTION: 'statuses.completed',
    APPLICATIONS_TABLE_STATUS_FILTER_CANCELLED_OPTION: 'statuses.cancelled',
    APPLICATIONS_TABLE_STATUS_FILTER_REJECTED_OPTION: 'statuses.rejected',
  },
};
