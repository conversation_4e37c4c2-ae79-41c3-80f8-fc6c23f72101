{"name": "esto-partner-cra", "version": "0.1.0", "private": true, "dependencies": {"@apollo/client": "3.9.11", "@chakra-ui/react": "2.10.9", "@chakra-ui/theme-tools": "2.2.6", "@emotion/react": "11.14.0", "@emotion/styled": "11.11.5", "@hookform/resolvers": "3.10.0", "@sentry/cli": "2.46.0", "@sentry/react": "7.108.0", "compose-function": "3.0.3", "copy-to-clipboard": "3.3.3", "date-fns": "3.6.0", "downshift": "9.0.9", "effector": "23.4.1", "effector-react": "23.3.0", "file-saver": "2.0.5", "framer-motion": "11.18.2", "graphql": "16.11.0", "history": "5.3.0", "i18next": "23.10.1", "i18next-browser-languagedetector": "7.2.2", "i18next-locize-backend": "6.4.3", "lodash": "4.17.21", "lottie-web": "5.13.0", "posthog-js": "1.249.5", "qrcode.react": "3.2.0", "query-string": "9.2.2", "react": "19.1.0", "react-color": "2.19.3", "react-day-picker": "9.8.0", "react-dom": "19.1.0", "react-ga4": "^2.1.0", "react-hook-form": "7.60.0", "react-i18next": "14.1.0", "react-icons": "5.5.0", "react-number-format": "5.4.4", "react-router-dom": "6.30.1", "web-vitals": "3.5.2", "zod": "3.25.75"}, "scripts": {"start": "env-cmd -e local vite", "start:dekker": "env-cmd -e dekker vite", "start:dekker-dev1": "env-cmd -e dekker-dev1 vite", "serve": "env-cmd -e local vite", "serve:domain": "HTTPS=true HOST=affiliate.esto.test SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e local vite", "serve:domain-dekker": "HTTPS=true HOST=affiliate.dekker.ee SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e dekker vite", "serve:domain-dev0": "HTTPS=true HOST=affiliate.dekker-dev0.ee SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e dekker-dev0 vite", "serve:domain-dev1": "HTTPS=true HOST=affiliate.dekker-dev1.ee SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e dekker-dev1 vite", "serve:domain-dev2": "HTTPS=true HOST=affiliate.dekker-dev2.ee SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e dekker-dev2 vite", "serve:domain-dev3": "HTTPS=true HOST=affiliate.dekker-dev3.ee SSL_CRT_FILE=affiliate.esto.test.crt SSL_KEY_FILE=affiliate.esto.test.key env-cmd -e dekker-dev3 vite", "certificate:mac": "sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain affiliate.esto.test.crt", "build": "vite build", "build:dekker": "env-cmd -e dekker-dev2 vite build", "build:dekker-dev1": "env-cmd -e dekker-dev1 vite build", "test": "vitest", "cypress:open": "env-cmd -e dekker-dev2 cypress open", "cypress:run": "env-cmd -e dekker-dev2 cypress run --browser chrome --component", "upload-src-maps": "node scripts/upload-src-maps.js", "print-version": "node scripts/print-version.js", "codegen": "env-cmd -e dekker-dev1 graphql-codegen --config codegen.ts", "codegen:watch": "env-cmd -e dekker-dev1 graphql-codegen --config codegen.ts --watch", "analyze": "vite build --mode analyze", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "lint-and-format": "pnpm run lint:fix && pnpm run format", "ts-check": "tsc --noEmit", "git-hooks-install": "lefthook install"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.30.1", "@faker-js/faker": "9.9.0", "@graphql-codegen/add": "5.0.2", "@graphql-codegen/cli": "5.0.7", "@graphql-codegen/near-operation-file-preset": "3.0.1", "@graphql-codegen/typescript": "4.0.9", "@graphql-codegen/typescript-graphql-request": "6.2.1", "@graphql-codegen/typescript-operations": "4.2.3", "@graphql-codegen/typescript-react-apollo": "4.3.3", "@parcel/watcher": "2.5.1", "@tanstack/eslint-plugin-query": "^5.81.2", "@types/compose-function": "0.0.33", "@types/file-saver": "2.0.7", "@types/lodash": "4.17.20", "@types/node": "24.0.10", "@types/qrcode.react": "1.0.5", "@types/react": "19.1.8", "@types/react-color": "3.0.13", "@types/react-dom": "19.1.6", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.6.0", "cypress": "13.7.1", "env-cmd": "10.1.0", "eslint": "9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "globals": "^16.3.0", "lefthook": "1.11.16", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^6.0.3", "typescript": "5.8.3", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.2.4"}}